import Image from 'next/image';
import { CheckCircleIcon } from '@heroicons/react/24/outline';

const usagePoints = [
  {
    id: 1,
    text: "Reduce dependency on traditional loan products",
  },
  {
    id: 2,
    text: "Manage cash cycles in industries like trading, logistics, Manufacturing and services",
  },
  {
    id: 3,
    text: "Take on larger purchase orders without waiting for earlier payments",
  },
  {
    id: 4,
    text: "Strengthen supplier relationships by paying them faster",
  }
];

export const SMEUsageSection = () => {
  return (
    <section className="bg-white py-16">
      <div className=" mx-auto pr-4 lg:pl-28 pl-4 sm:pl-8 md:pl-12">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-20 items-center">
          {/* Left side - Content */}
          <div>
            <div className="mb-6">
              <Image
                src="/invoice-discounting/coins.svg"
                alt="Stack of coins icon"
                width={114}
                height={70}
              />
            </div>
            <h2 className="text-[28px] font-extrabold text-gray-900 mb-4">
              How SMEs are using it in Qatar and the GCC
            </h2>
            <p className="text-lg text-[#161C2D] mb-8">
              Across Qatar and the wider GCC, invoice discounting is gaining traction among SMEs that want to:
            </p>
            <div className="space-y-4 mb-8">
              {usagePoints.map((point) => (
                <div key={point.id} className="flex items-start space-x-3">
                  <CheckCircleIcon className="w-6 h-6 text-light-green flex-shrink-0 mt-0.5" />
                  <p className="text-[#161C2D] leading-relaxed text-lg">
                    {point.text}
                  </p>
                </div>
              ))}
            </div>
            <p className="text-lg text-[#161C2D]">
              At Madad, we see more SMEs turning to invoice discounting as a strategic tool, not just an emergency fix.
            </p>
          </div>

          {/* Right side - Illustration */}
          <div className="relative flex items-center justify-center">
             <div className="absolute w-full h-full bg-green-50 rounded-full blur-2xl opacity-50 "></div>
             <div className="relative w-full max-w-96 lg:max-w-none">
                <Image
                    src="/invoice-discounting/bank.svg"
                    alt="Bank illustration"
                    width={550}
                    height={475}
                    className="w-full h-auto"
                />
             </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default SMEUsageSection;
